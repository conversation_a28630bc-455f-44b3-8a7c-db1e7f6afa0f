#!/usr/bin/env python3
"""
YAML Configuration Processor with Variable Support

This utility processes YAML configuration files with variable support:
1. YAML anchors and aliases (&variable, *variable)
2. String interpolation using template syntax {variable}
3. Environment variable substitution

Usage:
    python yaml_processor.py config.yaml
    
Example YAML with variables:
```yaml
# Variables section
variables:
  model_name: &model_name "gpt-4.1-mini"
  base_dir: &base_dir "results"

# Use variables
api_model_info:
  model_name: *model_name  # YAML anchor reference
  
output_dir: "{base_dir}/{model_name}/task"  # String interpolation
```
"""

import yaml
import os
import sys
import re
from typing import Dict, Any, Union
from pathlib import Path


class YAMLProcessor:
    """Process YAML files with variable support"""
    
    def __init__(self):
        self.variables = {}
    
    def load_yaml(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Load YAML file and process variables
        
        Args:
            file_path: Path to YAML file
            
        Returns:
            Processed configuration dictionary
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"YAML file not found: {file_path}")
        
        # Load YAML with anchor support
        with open(file_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Extract variables from the config
        if 'variables' in config:
            self.variables.update(config['variables'])
            # Remove variables section from final config
            del config['variables']
        
        # Process string interpolation
        config = self._process_interpolation(config)
        
        return config
    
    def _process_interpolation(self, obj: Any) -> Any:
        """
        Recursively process string interpolation in configuration
        
        Args:
            obj: Configuration object (dict, list, str, etc.)
            
        Returns:
            Processed object with interpolated strings
        """
        if isinstance(obj, dict):
            return {key: self._process_interpolation(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._process_interpolation(item) for item in obj]
        elif isinstance(obj, str):
            return self._interpolate_string(obj)
        else:
            return obj
    
    def _interpolate_string(self, text: str) -> str:
        """
        Interpolate variables in a string using {variable} syntax
        
        Args:
            text: String with potential variable references
            
        Returns:
            String with variables interpolated
        """
        # Find all {variable} patterns
        pattern = r'\{([^}]+)\}'
        matches = re.findall(pattern, text)
        
        result = text
        for var_name in matches:
            # Try to get value from variables, then environment variables
            if var_name in self.variables:
                value = str(self.variables[var_name])
            elif var_name in os.environ:
                value = os.environ[var_name]
            else:
                print(f"Warning: Variable '{var_name}' not found, keeping as-is")
                continue
            
            result = result.replace(f'{{{var_name}}}', value)
        
        return result
    
    def save_processed_yaml(self, config: Dict[str, Any], output_path: Union[str, Path]):
        """
        Save processed configuration to YAML file
        
        Args:
            config: Processed configuration dictionary
            output_path: Output file path
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        print(f"Processed YAML saved to: {output_path}")


def main():
    """Command line interface"""
    if len(sys.argv) != 2:
        print("Usage: python yaml_processor.py <yaml_file>")
        print("Example: python yaml_processor.py config/evaluate_spatial_with_variables.yaml")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    try:
        processor = YAMLProcessor()
        config = processor.load_yaml(input_file)
        
        # Print processed configuration
        print("=== Processed Configuration ===")
        print(yaml.dump(config, default_flow_style=False, indent=2))
        
        # Optionally save processed config
        output_file = Path(input_file).with_suffix('.processed.yaml')
        processor.save_processed_yaml(config, output_file)
        
    except Exception as e:
        print(f"Error processing YAML file: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
